<script lang="ts" setup>
import { ref } from 'vue';
import { useSpacesStore } from '@/stores/spacesStore';
import {
	CircleClose,
	Collection,
	Expand,
	Fold,
	FolderAdd,
	Setting,
	User,
} from '@element-plus/icons-vue';
import { getAuth, type User as FirebaseUser } from 'firebase/auth';
import { inject, type Ref } from 'vue';
import MenuSpace from './MenuSpace.vue';
import DialogNewSpace from './DialogNewSpace.vue';
import { ElMessage } from 'element-plus';

const spacesStore = useSpacesStore();

const user = inject<Ref<FirebaseUser | null>>('user') as Ref<FirebaseUser | null>;
const isCollapse = ref(true);
const menuClosed = ref(true);
const dialog = ref<InstanceType<typeof DialogNewSpace>>();

const signOutUser = async () => {
	try {
		await getAuth().signOut();
		ElMessage.success('Signed out successfully.');
		window.location.href = '/landing/';
	} catch (error: any) {
		console.error("Sign Out Error:", error);
		ElMessage.error(`Sign Out Error: ${error.message || 'Unknown error'}`);
	}
};
</script>

<template>
	<el-menu router class="el-menu-vertical"
		:collapse="isCollapse"
		@open="() => menuClosed = false"
		@close="() => menuClosed = true"
	>
		<el-menu-item @click="isCollapse = !isCollapse">
			<el-icon>
				<expand v-if="isCollapse" />
				<fold v-else />
			</el-icon>
			<template #title>{{ isCollapse ? 'Expand' : 'Collapse' }}</template>
		</el-menu-item>

		<el-divider />
		
		<!-- Spaces Submenu -->
		<el-sub-menu index="spaces">
			<template #title>
				<el-icon><collection /></el-icon>
				<span>Spaces</span>
			</template>
			
			<el-menu-item 
				v-for="[spaceId, space] of Object.entries(spacesStore.spaces)" 
				:key="spaceId"
				:index="`/${spaceId}`"
				@click="spacesStore.setCurrentSpace(spaceId)"
				:class="{ 'active-space': spaceId === $route.params.spaceId }"
			>
				{{ space.name }}
			</el-menu-item>
			
			<el-menu-item @click="() => dialog?.show()">
				<el-icon><folder-add /></el-icon>
				<span>New Space</span>
			</el-menu-item>
		</el-sub-menu>

		<el-divider />

		<MenuSpace :menuClosed="menuClosed" />
		
		<span class="spacer" />

		<el-divider />

		<el-sub-menu index="user">
			<template #title>
				<el-icon size="24">
					<el-avatar :size="24" :src="user?.photoURL" />
				</el-icon>
				<span>{{ user?.displayName }}</span>
			</template>

			<el-menu-item index="/profile">
				<el-icon>
					<User />
				</el-icon>
				<span>Profile</span>
			</el-menu-item>
			<el-menu-item @click="signOutUser">
				<el-icon>
					<CircleClose />
				</el-icon>
				<span>Log out</span>
			</el-menu-item>	
		</el-sub-menu>
	
		<el-menu-item index="/settings">
			<el-icon>
				<setting />
			</el-icon>
			<template #title>Settings</template>
		</el-menu-item>
	</el-menu>
	<DialogNewSpace ref="dialog" />
</template>

<style>
.el-menu-vertical {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.el-menu-vertical:not(.el-menu--collapse) {
	width: 240px;
}
.el-divider--horizontal {
	margin: 0;
}
.el-menu-item,
.el-sub-menu__title {
	gap: 16px;
}
.el-menu--popup {
	padding: 0px;
}
.spacer {
	flex-grow: 1;
}
.active-space {
	font-weight: bold;
	color: var(--el-color-primary) !important;
}
</style>
