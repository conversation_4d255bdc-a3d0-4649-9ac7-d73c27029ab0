<script setup lang="ts">
import { computed } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import type { Column, Task, TaskStatus } from '@/types'
import draggable from 'vuedraggable';
import KanbanCard from './KanbanCard.vue';
import TaskStatusIcon from '@/components/TaskStatusIcon.vue';

const props = defineProps<{
	column: Column;
	tasks: Task[];
}>();

const emit = defineEmits<{
        (e: 'add-task', columnId: TaskStatus): void;
        (e: 'edit-task', task: Task): void;
        (e: 'delete-task', taskId: string): void;
        (e: 'move-task', taskId: string, newColumnId: string, newPosition: number): void;
}>();

// Create a computed property for the task list to work with vuedraggable
const taskList = computed({
	get: () => props.tasks,
	set: () => {
		// This will be triggered when the order changes within the same column
		// We don't need to do anything here as the actual reordering is handled in onChange
	}
});

// Handle change event from vuedraggable
function onChange(event: any) {
	// Handle different types of changes
	if (event.added) {
		// Task was added to this column from another column
		const { newIndex, element } = event.added;
		const taskId = element.id;

		if (!taskId) return;

		const targetColumnId = props.column.id!;

		emit('move-task', taskId, targetColumnId, newIndex);
	}
	else if (event.moved) {
		// Task was moved within this column
		const { newIndex, element } = event.moved;
		const taskId = element.id;

		if (!taskId) return;

		const targetColumnId = props.column.id!;

		emit('move-task', taskId, targetColumnId, newIndex);
	}
	else if (event.removed) {
		// Task was removed from this column (moved to another column)
		// This is handled by the 'added' event in the target column
	}
}

const editTask = (task: Task) => emit('edit-task', task);
const deleteTask = (taskId: string) => emit('delete-task', taskId);
</script>

<template>
	<div class="kanban-column">
		<div class="column-header">
			<div id="header-title" class="flex">
				<TaskStatusIcon :status="column.id" />
				<h3>{{ column.name }}</h3>
			</div>
			<el-button type="primary" plain @click="emit('add-task', column.id)">
				<el-icon>
					<Plus />
				</el-icon> Add Task
			</el-button>
		</div>

		<div class="tasks-container">
			<div class="task-list">
				<draggable v-model="taskList" :group="{ name: 'tasks', pull: true, put: true }" item-key="id"
					class="task-list-draggable" ghost-class="ghost-card" chosen-class="chosen-card" @change="onChange">
					<template #header v-if="tasks.length === 0">
						<div class="empty-tasks">
							<p>No tasks yet</p>
							<p class="drag-hint">Drag tasks here</p>
						</div>
					</template>
					<template #item="{ element: task, index }">
						<KanbanCard class="task-card" :data-id="task.id" :data-index="index"
							:content="task"
							@edit-task="editTask"
							@delete-task="deleteTask"
						/>
					</template>
				</draggable>
			</div>
		</div>
	</div>
</template>

<style scoped>
.kanban-column {
	display: flex;
	flex-direction: column;
	width: 25%;
	min-width: 224px;
}

.column-header {
	padding: 12px;
	border-radius: 4px 4px 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

#header-title {
	gap: 8px;
	line-height: 18px;
}

#header-title > h3 {
	margin: 0;
}

.tasks-container {
	flex: 1;
	overflow-y: auto;
	padding: 8px;
}

.task-list {
	min-height: 10px;
}

.task-list-draggable {
	min-height: 10px;
}

.ghost-card {
	opacity: 0.5;
}

.empty-tasks {
	text-align: center;
	color: var(--lt4);
	padding: 20px 0;
	border: 2px dashed #dcdfe6;
	border-radius: 4px;
	margin-bottom: 8px;
}

.drag-hint {
	font-size: 14px;
	color: var(--lt2);
	margin-top: 8px;
}
</style>
