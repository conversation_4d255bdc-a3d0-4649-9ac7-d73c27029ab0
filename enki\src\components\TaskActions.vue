<script setup lang="ts">
import { Edit, Delete } from '@element-plus/icons-vue';

const emit = defineEmits<{
        (e: 'edit-task'): void;
        (e: 'delete-task'): void;
}>();
</script>
<template>
    <div class="task-actions">
        <el-button type="text" size="small" @click="emit('edit-task')">
            <el-icon>
                <Edit />
            </el-icon>
        </el-button>
        <el-button type="text" size="small" @click="emit('delete-task')">
            <el-icon>
                <Delete />
            </el-icon>
        </el-button>
    </div>
</template>

<style scoped>
.task-actions {
	display: flex;
    gap: 4px;
}

.task-actions .el-button {
	padding: 0px;
    margin: 0px;
    width: 16px;
}
</style>