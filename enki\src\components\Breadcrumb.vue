<script setup lang="ts">
import { useKanbanStore } from '@/stores/kanbanStore';
import { useSpacesStore } from '@/stores/spacesStore';
import { ArrowRight } from '@element-plus/icons-vue';
import { computed } from 'vue';
import { useRoute, type RouteRecordNameGeneric } from 'vue-router';

const route = useRoute();
const spacesStore = useSpacesStore();
const kanbanStore = useKanbanStore();
const spaceId = computed(() => route.params.spaceId as string | undefined);

type BreadcrumbItem = {
    label: RouteRecordNameGeneric;
    to: string;
};

function getSpacePath(spaceId: string): BreadcrumbItem[] {
    const crumb = [{
        label: spacesStore.spaces[spaceId].name as RouteRecordNameGeneric,
        to: `/${spaceId}`,
    }];
    switch (route.name) {
        case 'Kanban':
            return crumb.concat(
                kanbanStore
                    .getTaskHierarchy(route.path.split('/').pop() as string)
                    .map((task) => ({
                        label: task.name,
                        to: `/${spaceId}/kanban/${task.id}`,
                    }))
            );
    }
    return crumb.concat(
        route.matched
            .map((routeItem) => ({
                label: routeItem.name,
                to: routeItem.path.split('/')
                    .map((segment) => segment.startsWith(':') 
                        ? route.params[segment.slice(1)]
                        : segment
                    )
                    .join('/'),
            }))
    );
}

const breadcrumbs = computed(() => {
    if (!spaceId.value) return route.matched
        .map((routeItem) => ({
            label: routeItem.name,
            to: routeItem.path,
        }));
    return getSpacePath(spaceId.value);
});
</script>

<template>
    <el-breadcrumb :separator-icon="ArrowRight">
        <el-breadcrumb-item v-for="(crumb, index) in breadcrumbs" :to="crumb.to" :key="index">
            <h1>{{ crumb.label }}</h1>
        </el-breadcrumb-item>
    </el-breadcrumb>
</template>