export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Column = {
	id: TaskStatus;
	name: string;
}

export type TaskStatus = 'todo' | 'doing' | 'review' | 'done';

export type Task = {
	id: string;
	name: string;
	status: TaskStatus;
	description?: string;
	tags: string[];
	assignees: string[];
	start_time?: string;
	end_time?: string;
	parent_task_id: string | undefined;
	position: number;
	blocking: string[];
	blocked_by: string[];
	created_at?: string;
	updated_at?: string;
}

export type Project = Task & { parent_task_id: undefined };

export type SpaceDefinition = {
    name: string;
    server: string;
}

export type Space = {
    projects: Project[];
}