K:\Prog\enki-mr\ki\target\debug\deps\ki.exe: src\main.rs src\api\mod.rs src\api\auth.rs src\api\routes.rs src\api\handlers\mod.rs src\api\handlers\firebase_handlers.rs src\api\handlers\task_handlers.rs src\db\mod.rs src\db\connection.rs src\db\models\mod.rs src\db\models\task.rs src\db\models\session.rs src\db\init.rs src\db\repositories\mod.rs src\db\repositories\task_repository.rs src\db\repositories\session_repository.rs

K:\Prog\enki-mr\ki\target\debug\deps\ki.d: src\main.rs src\api\mod.rs src\api\auth.rs src\api\routes.rs src\api\handlers\mod.rs src\api\handlers\firebase_handlers.rs src\api\handlers\task_handlers.rs src\db\mod.rs src\db\connection.rs src\db\models\mod.rs src\db\models\task.rs src\db\models\session.rs src\db\init.rs src\db\repositories\mod.rs src\db\repositories\task_repository.rs src\db\repositories\session_repository.rs

src\main.rs:
src\api\mod.rs:
src\api\auth.rs:
src\api\routes.rs:
src\api\handlers\mod.rs:
src\api\handlers\firebase_handlers.rs:
src\api\handlers\task_handlers.rs:
src\db\mod.rs:
src\db\connection.rs:
src\db\models\mod.rs:
src\db\models\task.rs:
src\db\models\session.rs:
src\db\init.rs:
src\db\repositories\mod.rs:
src\db\repositories\task_repository.rs:
src\db\repositories\session_repository.rs:
