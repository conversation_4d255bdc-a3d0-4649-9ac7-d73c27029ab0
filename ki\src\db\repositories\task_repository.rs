use crate::db::{Database, DatabaseConnection};
use crate::db::models::{Task, NewTask, UpdateTask};
use anyhow::Result;
use chrono::Utc;
use sqlx::types::<PERSON>son;
use uuid::Uuid;

/// Task repository
#[derive(Debug, Clone)]
pub struct TaskRepository {
    db: Database,
}

impl TaskRepository {
    /// Create a new task repository
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new task
    pub async fn create(&self, new_task: NewTask) -> Result<Task> {
        let id = Uuid::new_v4();
        let now = Utc::now();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Prepare values to avoid temporary value issues
                let name = new_task.name.clone();
                let status_param = new_task.status;
                let description = new_task.description.clone();
                let tags_param = Json(new_task.tags.clone());
                let assignees_param = Json(new_task.assignees.clone());
                let start_time = new_task.start_time;
                let end_time = new_task.end_time;
                let parent_task_id = new_task.parent_task_id;
                let position = new_task.position;

                let task = sqlx::query_as::<_, Task>(
                    r#"
                    INSERT INTO tasks (
                        id, name, status, description, tags, assignees,
                        start_time, end_time, parent_task_id, position,
                        created_at, updated_at
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(id)
                .bind(name)
                .bind(status_param)
                .bind(description)
                .bind(tags_param)
                .bind(assignees_param)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(now)
                .bind(now)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    INSERT INTO tasks (
                        id, name, status, description, tags, assignees,
                        start_time, end_time, parent_task_id, position,
                        created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(id)
                .bind(new_task.name)
                .bind(new_task.status)
                .bind(new_task.description)
                .bind(Json(new_task.tags))
                .bind(Json(new_task.assignees))
                .bind(new_task.start_time)
                .bind(new_task.end_time)
                .bind(new_task.parent_task_id)
                .bind(new_task.position)
                .bind(now)
                .bind(now)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get a task by ID
    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    // Get all tasks
    pub async fn get_all(&self) -> Result<Vec<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    FROM tasks
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update a task
    pub async fn update(&self, id: Uuid, update_task: UpdateTask) -> Result<Task> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Get current task
                let current = self.get_by_id(id).await?
                    .ok_or_else(|| anyhow::anyhow!("Task not found"))?;

                // Prepare values to avoid temporary value issues
                let name = update_task.name.unwrap_or_else(|| current.name.clone());
                let status_param = update_task.status.unwrap_or(current.status);
                let description = update_task.description.or(current.description.clone());
                let tags_param = Json(update_task.tags.unwrap_or_else(|| current.tags.0.clone()));
                let assignees_param = Json(update_task.assignees.unwrap_or_else(|| current.assignees.0.clone()));
                let start_time = update_task.start_time.or(current.start_time);
                let end_time = update_task.end_time.or(current.end_time);
                let parent_task_id = update_task.parent_task_id.or(current.parent_task_id);
                let position = update_task.position.unwrap_or(current.position);
                let updated_at = Utc::now();

                // Update task
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    UPDATE tasks
                    SET
                        name = ?,
                        status = ?,
                        description = ?,
                        tags = ?,
                        assignees = ?,
                        start_time = ?,
                        end_time = ?,
                        parent_task_id = ?,
                        position = ?,
                        updated_at = ?
                    WHERE id = ?
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(name)
                .bind(status_param)
                .bind(description)
                .bind(tags_param)
                .bind(assignees_param)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(updated_at)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // Get current task
                let current = self.get_by_id(id).await?
                    .ok_or_else(|| anyhow::anyhow!("Task not found"))?;

                // Prepare values to avoid temporary value issues
                let name = update_task.name.unwrap_or_else(|| current.name.clone());
                let status = update_task.status.unwrap_or(current.status);
                let description = update_task.description.or(current.description.clone());
                let tags = Json(update_task.tags.unwrap_or_else(|| current.tags.0.clone()));
                let assignees = Json(update_task.assignees.unwrap_or_else(|| current.assignees.0.clone()));
                let start_time = update_task.start_time.or(current.start_time);
                let end_time = update_task.end_time.or(current.end_time);
                let parent_task_id = update_task.parent_task_id.or(current.parent_task_id);
                let position = update_task.position.unwrap_or(current.position);
                let updated_at = Utc::now();

                // Update task
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    UPDATE tasks
                    SET
                        name = $1,
                        status = $2,
                        description = $3,
                        tags = $4,
                        assignees = $5,
                        start_time = $6,
                        end_time = $7,
                        parent_task_id = $8,
                        position = $9,
                        updated_at = $10
                    WHERE id = $11
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        created_at,
                        updated_at
                    "#
                )
                .bind(name)
                .bind(status)
                .bind(description)
                .bind(tags)
                .bind(assignees)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(updated_at)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete a task and all its sub-tasks recursively
    pub async fn delete(&self, id: Uuid) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query(
                    r#"
                    WITH RECURSIVE sub_tasks(id) AS (
                        SELECT id FROM tasks WHERE id = ?1
                        UNION ALL
                        SELECT t.id FROM tasks t
                        JOIN sub_tasks st ON t.parent_task_id = st.id
                    )
                    DELETE FROM tasks WHERE id IN (SELECT id FROM sub_tasks);
                    "#
                )
                .bind(id)
                .execute(pool)
                .await?;

                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query(
                    r#"
                    WITH RECURSIVE sub_tasks(id) AS (
                        SELECT id FROM tasks WHERE id = $1
                        UNION ALL
                        SELECT t.id FROM tasks t
                        JOIN sub_tasks st ON t.parent_task_id = st.id
                    )
                    DELETE FROM tasks WHERE id IN (SELECT id FROM sub_tasks);
                    "#
                )
                .bind(id)
                .execute(pool)
                .await?;

                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}