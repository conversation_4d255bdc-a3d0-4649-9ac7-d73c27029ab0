use crate::db::{Database, DatabaseConnection};
use crate::db::models::{Task, NewTask, UpdateTask};
use anyhow::Result;
use chrono::Utc;
use sqlx::{types::<PERSON><PERSON>, <PERSON>};
use uuid::Uuid;

/// Task repository
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct TaskRepository {
    db: Database,
}

impl TaskRepository {
    /// Create a new task repository
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new task
    pub async fn create(&self, new_task: NewTask) -> Result<Task> {
        let id = Uuid::new_v4();
        let now = Utc::now();

        // Validate dependencies
        self.validate_dependencies(&id, &new_task.blocking, &new_task.blocked_by)?;

        // For new tasks, synchronize dependencies (from empty arrays to new arrays)
        self.synchronize_dependencies(id, &[], &new_task.blocking, &[], &new_task.blocked_by).await?;

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Prepare values to avoid temporary value issues
                let name = new_task.name.clone();
                let status_param = new_task.status;
                let description = new_task.description.clone();
                let tags_param = Json(new_task.tags.clone());
                let assignees_param = Json(new_task.assignees.clone());
                let blocking_param = Json(new_task.blocking.clone());
                let blocked_by_param = Json(new_task.blocked_by.clone());
                let start_time = new_task.start_time;
                let end_time = new_task.end_time;
                let parent_task_id = new_task.parent_task_id;
                let position = new_task.position;

                let task = sqlx::query_as::<_, Task>(
                    r#"
                    INSERT INTO tasks (
                        id, name, status, description, tags, assignees,
                        start_time, end_time, parent_task_id, position,
                        blocking, blocked_by, created_at, updated_at
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    "#
                )
                .bind(id)
                .bind(name)
                .bind(status_param)
                .bind(description)
                .bind(tags_param)
                .bind(assignees_param)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(blocking_param)
                .bind(blocked_by_param)
                .bind(now)
                .bind(now)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    INSERT INTO tasks (
                        id, name, status, description, tags, assignees,
                        start_time, end_time, parent_task_id, position,
                        blocking, blocked_by, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    "#
                )
                .bind(id)
                .bind(new_task.name)
                .bind(new_task.status)
                .bind(new_task.description)
                .bind(Json(new_task.tags))
                .bind(Json(new_task.assignees))
                .bind(new_task.start_time)
                .bind(new_task.end_time)
                .bind(new_task.parent_task_id)
                .bind(new_task.position)
                .bind(Json(new_task.blocking))
                .bind(Json(new_task.blocked_by))
                .bind(now)
                .bind(now)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get a task by ID
    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    FROM tasks
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    // Get all tasks
    pub async fn get_all(&self) -> Result<Vec<Task>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    FROM tasks
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let tasks = sqlx::query_as::<_, Task>(
                    r#"
                    SELECT
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    FROM tasks
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(tasks)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update a task
    pub async fn update(&self, id: Uuid, update_task: UpdateTask) -> Result<Task> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Get current task
                let current = self.get_by_id(id).await?
                    .ok_or_else(|| anyhow::anyhow!("Task not found"))?;

                // Prepare values to avoid temporary value issues
                let name = update_task.name.unwrap_or_else(|| current.name.clone());
                let status_param = update_task.status.unwrap_or(current.status);
                let description = update_task.description.or(current.description.clone());
                let tags_param = Json(update_task.tags.unwrap_or_else(|| current.tags.0.clone()));
                let assignees_param = Json(update_task.assignees.unwrap_or_else(|| current.assignees.0.clone()));
                let blocking = update_task.blocking.unwrap_or_else(|| current.blocking.0.clone());
                let blocked_by = update_task.blocked_by.unwrap_or_else(|| current.blocked_by.0.clone());
                let start_time = update_task.start_time.or(current.start_time);
                let end_time = update_task.end_time.or(current.end_time);
                let parent_task_id = update_task.parent_task_id.or(current.parent_task_id);
                let position = update_task.position.unwrap_or(current.position);
                let updated_at = Utc::now();

                // Validate dependencies
                self.validate_dependencies(&id, &blocking, &blocked_by)?;

                // Synchronize bidirectional dependencies
                self.synchronize_dependencies(id, &current.blocking.0, &blocking, &current.blocked_by.0, &blocked_by).await?;

                let blocking_param = Json(blocking);
                let blocked_by_param = Json(blocked_by);

                // Update task
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    UPDATE tasks
                    SET
                        name = ?,
                        status = ?,
                        description = ?,
                        tags = ?,
                        assignees = ?,
                        start_time = ?,
                        end_time = ?,
                        parent_task_id = ?,
                        position = ?,
                        blocking = ?,
                        blocked_by = ?,
                        updated_at = ?
                    WHERE id = ?
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    "#
                )
                .bind(name)
                .bind(status_param)
                .bind(description)
                .bind(tags_param)
                .bind(assignees_param)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(blocking_param)
                .bind(blocked_by_param)
                .bind(updated_at)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // Get current task
                let current = self.get_by_id(id).await?
                    .ok_or_else(|| anyhow::anyhow!("Task not found"))?;

                // Prepare values to avoid temporary value issues
                let name = update_task.name.unwrap_or_else(|| current.name.clone());
                let status = update_task.status.unwrap_or(current.status);
                let description = update_task.description.or(current.description.clone());
                let tags = Json(update_task.tags.unwrap_or_else(|| current.tags.0.clone()));
                let assignees = Json(update_task.assignees.unwrap_or_else(|| current.assignees.0.clone()));
                let blocking_vec = update_task.blocking.unwrap_or_else(|| current.blocking.0.clone());
                let blocked_by_vec = update_task.blocked_by.unwrap_or_else(|| current.blocked_by.0.clone());
                let start_time = update_task.start_time.or(current.start_time);
                let end_time = update_task.end_time.or(current.end_time);
                let parent_task_id = update_task.parent_task_id.or(current.parent_task_id);
                let position = update_task.position.unwrap_or(current.position);
                let updated_at = Utc::now();

                // Validate dependencies
                self.validate_dependencies(&id, &blocking_vec, &blocked_by_vec)?;

                // Synchronize bidirectional dependencies
                self.synchronize_dependencies(id, &current.blocking.0, &blocking_vec, &current.blocked_by.0, &blocked_by_vec).await?;

                let blocking = Json(blocking_vec);
                let blocked_by = Json(blocked_by_vec);

                // Update task
                let task = sqlx::query_as::<_, Task>(
                    r#"
                    UPDATE tasks
                    SET
                        name = $1,
                        status = $2,
                        description = $3,
                        tags = $4,
                        assignees = $5,
                        start_time = $6,
                        end_time = $7,
                        parent_task_id = $8,
                        position = $9,
                        blocking = $10,
                        blocked_by = $11,
                        updated_at = $12
                    WHERE id = $13
                    RETURNING
                        id,
                        name,
                        status,
                        description,
                        tags,
                        assignees,
                        start_time,
                        end_time,
                        parent_task_id,
                        position,
                        blocking,
                        blocked_by,
                        created_at,
                        updated_at
                    "#
                )
                .bind(name)
                .bind(status)
                .bind(description)
                .bind(tags)
                .bind(assignees)
                .bind(start_time)
                .bind(end_time)
                .bind(parent_task_id)
                .bind(position)
                .bind(blocking)
                .bind(blocked_by)
                .bind(updated_at)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(task)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete a task and all its sub-tasks recursively
    pub async fn delete(&self, id: Uuid) -> Result<()> {
        // First, get all tasks that will be deleted (including sub-tasks)
        let tasks_to_delete = self.get_tasks_to_delete_recursively(id).await?;

        // Remove all these tasks from dependency arrays of other tasks
        for task_id in &tasks_to_delete {
            self.remove_from_all_dependencies(*task_id).await?;
        }

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query(
                    r#"
                    WITH RECURSIVE sub_tasks(id) AS (
                        SELECT id FROM tasks WHERE id = ?1
                        UNION ALL
                        SELECT t.id FROM tasks t
                        JOIN sub_tasks st ON t.parent_task_id = st.id
                    )
                    DELETE FROM tasks WHERE id IN (SELECT id FROM sub_tasks);
                    "#
                )
                .bind(id)
                .execute(pool)
                .await?;

                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query(
                    r#"
                    WITH RECURSIVE sub_tasks(id) AS (
                        SELECT id FROM tasks WHERE id = $1
                        UNION ALL
                        SELECT t.id FROM tasks t
                        JOIN sub_tasks st ON t.parent_task_id = st.id
                    )
                    DELETE FROM tasks WHERE id IN (SELECT id FROM sub_tasks);
                    "#
                )
                .bind(id)
                .execute(pool)
                .await?;

                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Validate task dependencies to prevent circular dependencies and self-references
    pub fn validate_dependencies(&self, task_id: &Uuid, blocking: &[String], blocked_by: &[String]) -> Result<()> {
        // Check for self-references
        let task_id_str = task_id.to_string();
        if blocking.contains(&task_id_str) {
            return Err(anyhow::anyhow!("Task cannot block itself"));
        }
        if blocked_by.contains(&task_id_str) {
            return Err(anyhow::anyhow!("Task cannot be blocked by itself"));
        }

        // Check for direct circular dependencies
        for blocking_task_id in blocking {
            if blocked_by.contains(blocking_task_id) {
                return Err(anyhow::anyhow!("Circular dependency detected: task {} cannot both block and be blocked by the same task", blocking_task_id));
            }
        }

        // Validate that all referenced task IDs exist and are valid UUIDs
        for task_id_str in blocking.iter().chain(blocked_by.iter()) {
            if Uuid::parse_str(task_id_str).is_err() {
                return Err(anyhow::anyhow!("Invalid task ID format: {}", task_id_str));
            }
        }

        Ok(())
    }

    /// Update a single task's dependency arrays (blocking or blocked_by)
    async fn update_task_dependencies(&self, task_id: Uuid, blocking: Option<Vec<String>>, blocked_by: Option<Vec<String>>) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Handle different combinations of updates
                match (blocking, blocked_by) {
                    (Some(blocking_vec), Some(blocked_by_vec)) => {
                        sqlx::query(
                            "UPDATE tasks SET blocking = ?, blocked_by = ?, updated_at = ? WHERE id = ?"
                        )
                        .bind(Json(blocking_vec))
                        .bind(Json(blocked_by_vec))
                        .bind(Utc::now())
                        .bind(task_id)
                        .execute(pool)
                        .await?;
                    }
                    (Some(blocking_vec), None) => {
                        sqlx::query(
                            "UPDATE tasks SET blocking = ?, updated_at = ? WHERE id = ?"
                        )
                        .bind(Json(blocking_vec))
                        .bind(Utc::now())
                        .bind(task_id)
                        .execute(pool)
                        .await?;
                    }
                    (None, Some(blocked_by_vec)) => {
                        sqlx::query(
                            "UPDATE tasks SET blocked_by = ?, updated_at = ? WHERE id = ?"
                        )
                        .bind(Json(blocked_by_vec))
                        .bind(Utc::now())
                        .bind(task_id)
                        .execute(pool)
                        .await?;
                    }
                    (None, None) => {
                        // Nothing to update
                        return Ok(());
                    }
                }
                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // Handle different combinations of updates
                match (blocking, blocked_by) {
                    (Some(blocking_vec), Some(blocked_by_vec)) => {
                        sqlx::query(
                            "UPDATE tasks SET blocking = $1, blocked_by = $2, updated_at = $3 WHERE id = $4"
                        )
                        .bind(Json(blocking_vec))
                        .bind(Json(blocked_by_vec))
                        .bind(Utc::now())
                        .bind(task_id)
                        .execute(pool)
                        .await?;
                    }
                    (Some(blocking_vec), None) => {
                        sqlx::query(
                            "UPDATE tasks SET blocking = $1, updated_at = $2 WHERE id = $3"
                        )
                        .bind(Json(blocking_vec))
                        .bind(Utc::now())
                        .bind(task_id)
                        .execute(pool)
                        .await?;
                    }
                    (None, Some(blocked_by_vec)) => {
                        sqlx::query(
                            "UPDATE tasks SET blocked_by = $1, updated_at = $2 WHERE id = $3"
                        )
                        .bind(Json(blocked_by_vec))
                        .bind(Utc::now())
                        .bind(task_id)
                        .execute(pool)
                        .await?;
                    }
                    (None, None) => {
                        // Nothing to update
                        return Ok(());
                    }
                }
                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Synchronize bidirectional dependencies when a task's dependencies change
    async fn synchronize_dependencies(&self, task_id: Uuid, old_blocking: &[String], new_blocking: &[String], old_blocked_by: &[String], new_blocked_by: &[String]) -> Result<()> {
        use std::collections::HashSet;

        let old_blocking_set: HashSet<&String> = old_blocking.iter().collect();
        let new_blocking_set: HashSet<&String> = new_blocking.iter().collect();
        let old_blocked_by_set: HashSet<&String> = old_blocked_by.iter().collect();
        let new_blocked_by_set: HashSet<&String> = new_blocked_by.iter().collect();

        let task_id_str = task_id.to_string();

        // Handle blocking relationships
        // Tasks that were removed from blocking list
        for removed_task_id in old_blocking_set.difference(&new_blocking_set) {
            if let Ok(target_id) = Uuid::parse_str(removed_task_id) {
                if let Ok(Some(target_task)) = self.get_by_id(target_id).await {
                    let mut updated_blocked_by = target_task.blocked_by.0.clone();
                    updated_blocked_by.retain(|id| id != &task_id_str);
                    self.update_task_dependencies(target_id, None, Some(updated_blocked_by)).await?;
                }
            }
        }

        // Tasks that were added to blocking list
        for added_task_id in new_blocking_set.difference(&old_blocking_set) {
            if let Ok(target_id) = Uuid::parse_str(added_task_id) {
                if let Ok(Some(target_task)) = self.get_by_id(target_id).await {
                    let mut updated_blocked_by = target_task.blocked_by.0.clone();
                    if !updated_blocked_by.contains(&task_id_str) {
                        updated_blocked_by.push(task_id_str.clone());
                    }
                    self.update_task_dependencies(target_id, None, Some(updated_blocked_by)).await?;
                }
            }
        }

        // Handle blocked_by relationships
        // Tasks that were removed from blocked_by list
        for removed_task_id in old_blocked_by_set.difference(&new_blocked_by_set) {
            if let Ok(target_id) = Uuid::parse_str(removed_task_id) {
                if let Ok(Some(target_task)) = self.get_by_id(target_id).await {
                    let mut updated_blocking = target_task.blocking.0.clone();
                    updated_blocking.retain(|id| id != &task_id_str);
                    self.update_task_dependencies(target_id, Some(updated_blocking), None).await?;
                }
            }
        }

        // Tasks that were added to blocked_by list
        for added_task_id in new_blocked_by_set.difference(&old_blocked_by_set) {
            if let Ok(target_id) = Uuid::parse_str(added_task_id) {
                if let Ok(Some(target_task)) = self.get_by_id(target_id).await {
                    let mut updated_blocking = target_task.blocking.0.clone();
                    if !updated_blocking.contains(&task_id_str) {
                        updated_blocking.push(task_id_str.clone());
                    }
                    self.update_task_dependencies(target_id, Some(updated_blocking), None).await?;
                }
            }
        }

        Ok(())
    }

    /// Get all task IDs that will be deleted recursively (including sub-tasks)
    async fn get_tasks_to_delete_recursively(&self, id: Uuid) -> Result<Vec<Uuid>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let rows = sqlx::query(
                    r#"
                    WITH RECURSIVE sub_tasks(id) AS (
                        SELECT id FROM tasks WHERE id = ?1
                        UNION ALL
                        SELECT t.id FROM tasks t
                        JOIN sub_tasks st ON t.parent_task_id = st.id
                    )
                    SELECT id FROM sub_tasks;
                    "#
                )
                .bind(id)
                .fetch_all(pool)
                .await?;

                let mut task_ids = Vec::new();
                for row in rows {
                    let task_id: Uuid = row.get("id");
                    task_ids.push(task_id);
                }
                Ok(task_ids)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let rows = sqlx::query(
                    r#"
                    WITH RECURSIVE sub_tasks(id) AS (
                        SELECT id FROM tasks WHERE id = $1
                        UNION ALL
                        SELECT t.id FROM tasks t
                        JOIN sub_tasks st ON t.parent_task_id = st.id
                    )
                    SELECT id FROM sub_tasks;
                    "#
                )
                .bind(id)
                .fetch_all(pool)
                .await?;

                let mut task_ids = Vec::new();
                for row in rows {
                    let task_id: Uuid = row.get("id");
                    task_ids.push(task_id);
                }
                Ok(task_ids)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Remove a task ID from all other tasks' dependency arrays
    async fn remove_from_all_dependencies(&self, deleted_task_id: Uuid) -> Result<()> {
        let deleted_task_id_str = deleted_task_id.to_string();

        // Get all tasks that might reference the deleted task
        let all_tasks = self.get_all().await?;

        for task in all_tasks {
            let mut blocking_updated = task.blocking.0.clone();
            let mut blocked_by_updated = task.blocked_by.0.clone();

            let blocking_changed = blocking_updated.len() != blocking_updated.iter().filter(|&id| id != &deleted_task_id_str).count();
            let blocked_by_changed = blocked_by_updated.len() != blocked_by_updated.iter().filter(|&id| id != &deleted_task_id_str).count();

            if blocking_changed || blocked_by_changed {
                blocking_updated.retain(|id| id != &deleted_task_id_str);
                blocked_by_updated.retain(|id| id != &deleted_task_id_str);

                self.update_task_dependencies(
                    task.id,
                    if blocking_changed { Some(blocking_updated) } else { None },
                    if blocked_by_changed { Some(blocked_by_updated) } else { None }
                ).await?;
            }
        }

        Ok(())
    }
}