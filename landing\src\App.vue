<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Picture } from '@element-plus/icons-vue';
import type { User } from "firebase/auth";
import {
  getAuth,
  onAuthStateChanged,
  signOut
} from "firebase/auth";
import AuthDialog from './components/AuthDialog.vue';

// Dark Mode
const isDark = ref(false);

// Auth State
const user = ref<User | null>(null);
const auth = getAuth();
const authDialogVisible = ref(false); // State to control the dialog visibility

// Open Auth Dialog
const openAuthDialog = () => {
  authDialogVisible.value = true;
};

// Sign Out (remains in App.vue as it controls the header button)
const signOutUser = async () => {
  try {
    await signOut(auth);
    ElMessage.success('Signed out successfully.');
  } catch (error: any) {
    console.error("Sign Out Error:", error);
    ElMessage.error(`Sign Out Error: ${error.message || 'Unknown error'}`);
  }
};

// Check authentication state when component mounts
onMounted(() => {
  onAuthStateChanged(auth, (currentUser) => {
    user.value = currentUser;
    if (currentUser) {
      authDialogVisible.value = false; // Close dialog if user is logged in (e.g., on refresh)
      window.location.href = '/enki/';
    }
  });
});
</script>

<template>
  <div id="app-layout" :class="{ dark: isDark }">
    <el-header class="app-header">
      <div class="logo">My SaaS</div>
      <div class="header-controls">
        <el-switch v-model="isDark" inline-prompt active-text="Dark" inactive-text="Light" size="large"
          style="--el-switch-on-color: var(--mag-dk3); --el-switch-off-color: var(--mag-lt3)" />
        <!-- Single Auth Button for Logged Out -->
        <nav v-if="!user" class="auth-nav">
          <el-button type="primary" @click="openAuthDialog">Sign In / Sign Up</el-button>
        </nav>
        <!-- User Info and Logout for Logged In -->
        <div v-else class="user-info">
          <span>Welcome, {{ user.displayName || user.email }}!</span>
          <el-button type="danger" plain @click="signOutUser">Sign Out</el-button>
        </div>
      </div>
    </el-header>

    <el-main class="app-main">
      <!-- Landing Page Content Sections -->
      <section class="hero">
        <el-row justify="center">
          <el-col :xs="22" :sm="20" :md="16" :lg="14">
            <h1 class="main-title">The Best Solution for Your Needs</h1>
            <p class="subtitle">Sign up now to get started and unlock amazing features that solve [Specific Problem].
            </p>
            <!-- Update Hero CTA to also open the dialog -->
            <el-button type="success" size="large" @click="openAuthDialog" class="cta-button">Try for Free</el-button>
            <!-- <el-button plain size="large" @click="watchDemo">Watch Demo</el-button> -->
          </el-col>
        </el-row>
      </section>

      <section class="visuals">
        <el-row justify="center">
          <el-col :xs="22" :sm="20" :md="18" :lg="16">
            <h2 class="section-title">See It In Action</h2>
            <div class="placeholder-visual">
              <p>[Placeholder for Product Screenshot / Video Demo]</p>
              <el-image style="width: 100%; max-width: 600px; height: auto; margin-top: 1rem; border-radius: 8px;"
                src="/placeholder-image.svg" alt="Product Visual Placeholder" fit="contain">
                <template #error>
                  <div class="image-slot"
                    style="display: flex; justify-content: center; align-items: center; height: 300px; background-color: var(--neu-lt2); border-radius: 8px;">
                    <el-icon>
                      <Picture />
                    </el-icon> Placeholder Image
                  </div>
                </template>
              </el-image>
            </div>
          </el-col>
        </el-row>
      </section>

      <section id="features" class="features">
        <el-row justify="center">
          <el-col :xs="22" :sm="20" :md="18" :lg="16">
            <h2 class="section-title">Why Choose Us? (Benefits)</h2>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="8">
                <el-card shadow="hover">
                  <h3>Benefit 1 Title</h3>
                  <p>Solve [Problem X] with our amazing capability.</p>
                </el-card>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <el-card shadow="hover">
                  <h3>Benefit 2 Title</h3>
                  <p>Experience unbeatable performance and reliability.</p>
                </el-card>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <el-card shadow="hover">
                  <h3>Benefit 3 Title</h3>
                  <p>Achieve seamless integration with your existing tools.</p>
                </el-card>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </section>

      <section class="social-proof">
        <el-row justify="center">
          <el-col :xs="22" :sm="20" :md="18" :lg="16">
            <h2 class="section-title">Trusted by Teams Worldwide</h2>
            <div class="placeholder-testimonials" style="margin-bottom: 2rem;">
              <el-card shadow="never" style="max-width: 600px; margin: auto;">
                <p>"This SaaS transformed how we work! Highly recommended."</p>
                <small>- Happy Customer, ACME Corp</small>
              </el-card>
            </div>
            <div class="placeholder-logos">
              <p style="margin-bottom: 1rem; color: var(--neu-dk1);">[Placeholder for Client Logos]</p>
              <el-space wrap size="large">
                <span class="client-logo-placeholder">Logo 1</span>
                <span class="client-logo-placeholder">Logo 2</span>
                <span class="client-logo-placeholder">Logo 3</span>
                <span class="client-logo-placeholder">Logo 4</span>
              </el-space>
            </div>
            <div class="placeholder-metrics" style="margin-top: 2rem;">
              <el-statistic title="Active Users" :value="10000" />
            </div>
          </el-col>
        </el-row>
      </section>

      <section class="pricing">
        <el-row justify="center">
          <el-col :xs="22" :sm="20" :md="18" :lg="16">
            <h2 class="section-title">Pricing</h2>
            <p>Simple and transparent pricing plans.</p>
            <div class="placeholder-pricing-table" style="margin: 2rem 0;">
              <el-alert title="Pricing details coming soon!" type="info" show-icon :closable="false" />
            </div>
          </el-col>
        </el-row>
      </section>

      <section class="faq">
        <el-row justify="center">
          <el-col :xs="22" :sm="20" :md="18" :lg="16">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <div class="placeholder-faq">
              <el-collapse accordion>
                <el-collapse-item title="Question 1: What is this?" name="1">
                  <div>Answer: It's the best SaaS ever!</div>
                </el-collapse-item>
                <el-collapse-item title="Question 2: How do I get started?" name="2">
                  <div>Answer: Just sign in using Google or GitHub and explore!</div>
                </el-collapse-item>
                <el-collapse-item title="Question 3: Is there a free trial?" name="3">
                  <div>Answer: Yes, the initial sign-up gives you access to basic features.</div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-col>
        </el-row>
      </section>
    </el-main>

    <el-footer class="app-footer">
      <div class="reassurance" style="margin-bottom: 1rem;">
        <el-link href="#" :underline="false">Privacy Policy</el-link> |
        <el-link href="#" :underline="false">Terms of Service</el-link>
        <p style="font-size: 0.8rem; margin-top: 0.5rem;">[Placeholder: Mention GDPR compliance or security measures]
        </p>
      </div>
      <p>&copy; {{ new Date().getFullYear() }} My SaaS. All rights reserved.</p>
    </el-footer>
    <AuthDialog v-model:visible="authDialogVisible" />
  </div>
</template>