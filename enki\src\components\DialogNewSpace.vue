<script lang="ts" setup>
import { ref } from 'vue';
// import { useSpacesStore } from '@/stores/spacesStore';

// const spacesStore = useSpacesStore();

const showNewSpaceDialog = ref(false);
const newSpaceName = ref('');

function createNewSpace() {
    if (!newSpaceName.value.trim()) return;
    // spacesStore.createNewSpace(newSpaceName.value);
    showNewSpaceDialog.value = false;
    newSpaceName.value = '';
}

defineExpose({
    show: () => {
        showNewSpaceDialog.value = true;
    }
});
</script>

<template>
    <el-dialog v-model="showNewSpaceDialog" title="Create New Space" width="30%">
        <el-form :model="newSpaceName" label-width="120px">
            <el-form-item label="Space Name" required>
                <el-input v-model="newSpaceName" placeholder="Enter space name" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="showNewSpaceDialog = false">Cancel</el-button>
                <el-button type="primary" @click="createNewSpace">Create</el-button>
            </span>
        </template>
    </el-dialog>
</template>
