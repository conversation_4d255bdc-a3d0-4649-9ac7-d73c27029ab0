<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useKanbanStore } from '@/stores/kanbanStore';
import { useSpacesStore } from '@/stores/spacesStore';
import {
	Menu as IconMenu,
	DataAnalysis,
	FolderAdd
} from '@element-plus/icons-vue';
import DialogTask from './DialogTask.vue';
import type { Task } from '@/types';

defineProps({
	menuClosed: Boolean,
});

const route = useRoute();
const router = useRouter();
const kanbanStore = useKanbanStore();
const spacesStore = useSpacesStore();
const currentSpaceId = computed(() => {
    // Get the current space ID from the route params
    const routeSpaceId = route.params.spaceId as string;
    return routeSpaceId || spacesStore.currentSpaceId;
});

const dialogProject = ref<Omit<Task, 'id'> | undefined>();
function showNewProjectDialog() {
	dialogProject.value = {
		name: '',
		description: '',
		status: 'todo',
		position: kanbanStore.getProjects.length,
		tags: [],
		assignees: [],
		start_time: undefined,
		end_time: undefined,
        parent_task_id: undefined,
        blocking: [],
        blocked_by: [],
	};
};

function onDialogClosed(task: Task | undefined) {
	dialogProject.value = undefined;
	if (task) {
		router.push(`/${currentSpaceId.value}/kanban/${task.id}`);
	}
}
</script>

<template>
    <el-menu-item :index="`/${currentSpaceId}`">
        <el-icon>
            <DataAnalysis />
        </el-icon>
        <template #title>Dashboard</template>
    </el-menu-item>

    <!-- Kanban Projects Submenu -->
    <el-sub-menu index="kanban">
        <template #title>
            <el-icon><icon-menu /></el-icon>
            <span>Kanban Projects</span>
        </template>

        <el-menu-item v-for="project in kanbanStore.getProjects" :key="project.id" :index="`/${currentSpaceId}/kanban/${project.id}`">
            {{ project.name }}
        </el-menu-item>

        <el-menu-item @click="showNewProjectDialog">
            <el-icon><folder-add /></el-icon>
            <span>New Project</span>
        </el-menu-item>
    </el-sub-menu>

	<DialogTask :task="dialogProject" @save="onDialogClosed" />
</template>
