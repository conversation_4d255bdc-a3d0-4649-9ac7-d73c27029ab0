# Task Dependencies Implementation Test Plan

## Backend Testing

### 1. Database Schema Verification
- Verify that both SQLite and PostgreSQL schemas include `blocking` and `blocked_by` fields
- Test database creation with new fields

### 2. Bidirectional Synchronization Testing

#### Test Case 1: Creating Task with Dependencies
```bash
# Create a project (task with no parent)
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Project",
    "status": "todo",
    "description": "Test project for dependencies",
    "tags": [],
    "assignees": [],
    "parent_task_id": null,
    "position": 0,
    "blocking": [],
    "blocked_by": []
  }'

# Create Task A
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Task A",
    "status": "todo",
    "description": "First task",
    "tags": [],
    "assignees": [],
    "parent_task_id": "PROJECT_ID",
    "position": 0,
    "blocking": [],
    "blocked_by": []
  }'

# Create Task B
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Task B",
    "status": "todo",
    "description": "Second task",
    "tags": [],
    "assignees": [],
    "parent_task_id": "PROJECT_ID",
    "position": 1,
    "blocking": [],
    "blocked_by": []
  }'
```

#### Test Case 2: Bidirectional Blocking Relationship
```bash
# Update Task A to block Task B
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_B_ID"]
  }'

# Verify Task B now has Task A in its blocked_by array
curl -X GET http://localhost:3000/tasks/TASK_B_ID

# Expected: Task B should have "blocked_by": ["TASK_A_ID"]
```

#### Test Case 3: Bidirectional Blocked-By Relationship
```bash
# Create Task C
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Task C",
    "status": "todo",
    "description": "Third task",
    "tags": [],
    "assignees": [],
    "parent_task_id": "PROJECT_ID",
    "position": 2,
    "blocking": [],
    "blocked_by": []
  }'

# Update Task C to be blocked by Task A
curl -X PUT http://localhost:3000/tasks/TASK_C_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocked_by": ["TASK_A_ID"]
  }'

# Verify Task A now has Task C in its blocking array
curl -X GET http://localhost:3000/tasks/TASK_A_ID

# Expected: Task A should have "blocking": ["TASK_B_ID", "TASK_C_ID"]
```

#### Test Case 4: Removing Dependencies
```bash
# Remove Task B from Task A's blocking list
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_C_ID"]
  }'

# Verify Task B no longer has Task A in its blocked_by array
curl -X GET http://localhost:3000/tasks/TASK_B_ID

# Expected: Task B should have "blocked_by": []
```

#### Test Case 5: Task Deletion Cleanup
```bash
# Delete Task A
curl -X DELETE http://localhost:3000/tasks/TASK_A_ID

# Verify Task C no longer has Task A in its blocked_by array
curl -X GET http://localhost:3000/tasks/TASK_C_ID

# Expected: Task C should have "blocked_by": []
```

### 3. Validation Testing
Test validation rules:

```bash
# Test self-reference (should fail)
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_A_ID"]
  }'

# Expected: HTTP 500 with error message "Task cannot block itself"

# Test circular dependency (should fail)
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_B_ID"],
    "blocked_by": ["TASK_B_ID"]
  }'

# Expected: HTTP 500 with error message about circular dependency

# Test invalid UUID format (should fail)
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["invalid-uuid"]
  }'

# Expected: HTTP 500 with error message "Invalid task ID format"
```

## Frontend Testing

### 1. Task Creation
- Create new tasks and verify `blocking` and `blocked_by` fields are initialized as empty arrays
- Verify task form includes dependency fields

### 2. Task Editing
- Open task edit dialog
- Verify dependency tree-select components are present
- Test adding/removing dependencies
- Verify save functionality includes dependency data

### 3. Task Display
- Verify KanbanCard shows dependency indicators
- Check that blocking tasks show lock icon
- Check that blocked tasks show warning icon

### 4. Data Flow
- Verify frontend sends dependency data to backend
- Verify backend responses include dependency data
- Verify frontend updates local state with dependency information

## Expected Results

### Backend
- ✅ Database schema includes new fields
- ✅ API endpoints accept and return dependency data
- ✅ Validation prevents circular dependencies and self-references
- ✅ Task creation/update works with dependencies
- ✅ **Bidirectional synchronization maintains consistency**
- ✅ **Task deletion removes references from all other tasks**

### Frontend
- ✅ Task type includes dependency fields
- ✅ Task forms initialize dependency fields
- ✅ Task edit dialog shows dependency management UI
- ✅ KanbanCard displays dependency indicators
- ✅ Data flows correctly between frontend and backend

## Key Implementation Features

### Bidirectional Synchronization
- When Task A is set to block Task B, Task B automatically gets Task A in its blocked_by array
- When Task C is set to be blocked by Task D, Task D automatically gets Task C in its blocking array
- Removing dependencies automatically updates both sides of the relationship
- All updates happen atomically to prevent inconsistent states

### Edge Case Handling
- Task deletion automatically removes the deleted task from all other tasks' dependency arrays
- Validation prevents self-references and direct circular dependencies
- Invalid UUID formats are rejected with clear error messages

### Transaction Safety
- All dependency updates are performed within the same database operation
- Synchronization happens before the main task update to ensure consistency
- Failed validations prevent any changes from being committed

## Manual Testing Steps

1. Start the Ki server
2. Open the Enki frontend
3. Create a new project
4. Create multiple tasks within the project
5. Edit a task and add dependencies using the tree-select
6. **Verify that the referenced tasks automatically show the reciprocal relationship**
7. **Test removing dependencies and verify both sides are updated**
8. **Delete a task and verify it's removed from all other tasks' dependencies**
9. Test validation by trying to create circular dependencies
10. Verify data persistence by refreshing the page
