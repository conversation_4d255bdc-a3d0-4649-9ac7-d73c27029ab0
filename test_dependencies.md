# Task Dependencies Implementation Test Plan

## Backend Testing

### 1. Database Schema Verification
- Verify that both SQLite and PostgreSQL schemas include `blocking` and `blocked_by` fields
- Test database creation with new fields

### 2. API Testing
Create test tasks with dependencies:

```bash
# Create a project (task with no parent)
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Project",
    "status": "todo",
    "description": "Test project for dependencies",
    "tags": [],
    "assignees": [],
    "parent_task_id": null,
    "position": 0,
    "blocking": [],
    "blocked_by": []
  }'

# Create Task A
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Task A",
    "status": "todo",
    "description": "First task",
    "tags": [],
    "assignees": [],
    "parent_task_id": "PROJECT_ID",
    "position": 0,
    "blocking": [],
    "blocked_by": []
  }'

# Create Task B that is blocked by Task A
curl -X POST http://localhost:3000/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Task B",
    "status": "todo",
    "description": "Second task blocked by Task A",
    "tags": [],
    "assignees": [],
    "parent_task_id": "PROJECT_ID",
    "position": 1,
    "blocking": [],
    "blocked_by": ["TASK_A_ID"]
  }'

# Update Task A to block Task B
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_B_ID"]
  }'
```

### 3. Validation Testing
Test validation rules:

```bash
# Test self-reference (should fail)
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_A_ID"]
  }'

# Test circular dependency (should fail)
curl -X PUT http://localhost:3000/tasks/TASK_A_ID \
  -H "Content-Type: application/json" \
  -d '{
    "blocking": ["TASK_B_ID"],
    "blocked_by": ["TASK_B_ID"]
  }'
```

## Frontend Testing

### 1. Task Creation
- Create new tasks and verify `blocking` and `blocked_by` fields are initialized as empty arrays
- Verify task form includes dependency fields

### 2. Task Editing
- Open task edit dialog
- Verify dependency tree-select components are present
- Test adding/removing dependencies
- Verify save functionality includes dependency data

### 3. Task Display
- Verify KanbanCard shows dependency indicators
- Check that blocking tasks show lock icon
- Check that blocked tasks show warning icon

### 4. Data Flow
- Verify frontend sends dependency data to backend
- Verify backend responses include dependency data
- Verify frontend updates local state with dependency information

## Expected Results

### Backend
- ✅ Database schema includes new fields
- ✅ API endpoints accept and return dependency data
- ✅ Validation prevents circular dependencies and self-references
- ✅ Task creation/update works with dependencies

### Frontend
- ✅ Task type includes dependency fields
- ✅ Task forms initialize dependency fields
- ✅ Task edit dialog shows dependency management UI
- ✅ KanbanCard displays dependency indicators
- ✅ Data flows correctly between frontend and backend

## Manual Testing Steps

1. Start the Ki server
2. Open the Enki frontend
3. Create a new project
4. Create multiple tasks within the project
5. Edit a task and add dependencies using the tree-select
6. Verify dependency indicators appear on kanban cards
7. Test validation by trying to create circular dependencies
8. Verify data persistence by refreshing the page
