use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{types::<PERSON><PERSON>, FromRow};
use uuid::Uuid;

/// Task status enum
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::Type)]
#[sqlx(rename_all = "lowercase")]
pub enum TaskStatus {
    #[serde(rename = "todo")]
    Todo,
    #[serde(rename = "doing")]
    Doing,
    #[serde(rename = "review")]
    Review,
    #[serde(rename = "done")]
    Done,
}

impl Default for TaskStatus {
    fn default() -> Self {
        Self::Todo
    }
}

impl From<&str> for TaskStatus {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "doing" => Self::Doing,
            "review" => Self::Review,
            "done" => Self::Done,
            _ => Self::Todo,
        }
    }
}

/// Task model
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: Uuid,
    pub name: String,
    pub status: TaskStatus,
    pub description: Option<String>,
    pub tags: Json<Vec<String>>,
    pub assignees: <PERSON><PERSON><Vec<String>>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub parent_task_id: Option<Uuid>,
    pub position: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// New task request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewTask {
    pub name: String,
    pub status: TaskStatus,
    pub description: Option<String>,
    pub tags: Vec<String>,
    pub assignees: Vec<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub parent_task_id: Option<Uuid>,
    pub position: i32,
}

/// Update task request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTask {
    pub name: Option<String>,
    pub status: Option<TaskStatus>,
    pub description: Option<String>,
    pub tags: Option<Vec<String>>,
    pub assignees: Option<Vec<String>>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub parent_task_id: Option<Uuid>,
    pub position: Option<i32>,
}

/// Task response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskResponse {
    pub id: String,
    pub name: String,
    pub status: String,
    pub description: Option<String>,
    pub tags: Vec<String>,
    pub assignees: Vec<String>,
    pub start_time: Option<String>,
    pub end_time: Option<String>,
    pub parent_task_id: Option<String>,
    pub position: i32,
    pub created_at: String,
    pub updated_at: String,
}

impl From<Task> for TaskResponse {
    fn from(task: Task) -> Self {
        Self {
            id: task.id.to_string(),
            name: task.name,
            status: format!("{:?}", task.status).to_lowercase(),
            description: task.description,
            tags: task.tags.0,
            assignees: task.assignees.0,
            start_time: task.start_time.map(|t| t.to_rfc3339()),
            end_time: task.end_time.map(|t| t.to_rfc3339()),
            parent_task_id: task.parent_task_id.map(|id| id.to_string()),

            position: task.position,
            created_at: task.created_at.to_rfc3339(),
            updated_at: task.updated_at.to_rfc3339(),
        }
    }
}
