import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';

const app = express();
const PORT = 5000;

// Add logging middleware to debug requests
app.use((req, _res, next) => {
  console.log(`Request: ${req.method} ${req.url}`);
  next();
});

// Proxy for the landing app
app.use('/landing', createProxyMiddleware({
  target: 'http://localhost:5174',
  changeOrigin: true,
  ws: true,
  logLevel: 'debug'
}));

// Proxy for the Enki app
app.use('/enki', createProxyMiddleware({
  target: 'http://localhost:5173',
  changeOrigin: true,
  ws: true,
  logLevel: 'debug'
}));

// Root path redirects to Landing app
app.get('/', (_req, res) => {
  res.redirect('/landing/');
});

app.listen(PORT, () => {
  console.log(`Proxy server running at http://localhost:${PORT}`);
  console.log(`- Landing app available at http://localhost:${PORT}/landing`);
  console.log(`- Enki app available at http://localhost:${PORT}/enki`);
});
