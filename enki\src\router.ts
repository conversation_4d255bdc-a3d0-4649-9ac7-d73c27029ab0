import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from './pages/Dashboard.vue'
import Kanban from './pages/Kanban/Kanban.vue'
import Settings from './pages/Settings.vue'
import Profile from './pages/Profile.vue'

const routes = [
	{
		path: '/:spaceId',
		name: 'Dashboard',
		component: Dashboard,
		meta: { requiresAuth: true },
		props: true
	},
	{
		path: '/:spaceId/kanban/:projectId',
		name: 'Kanban',
		component: Kanban,
		meta: { requiresAuth: true },
		props: true
	},
	{
		path: '/profile',
		name: 'Profile',
		component: Profile,
		meta: { requiresAuth: true },
	},
	{
		path: '/settings',
		name: 'Settings',
		component: Settings,
	}
];

const router = createRouter({
	history: createWebHistory("enki"),
	routes,
});

function init(user: any) {
	router.beforeEach((to, _from, next) => {
		if (
			!to.matched.some(record => record.meta.requiresAuth)
			|| user.value
		) return next();
	});
}

export { init, router };